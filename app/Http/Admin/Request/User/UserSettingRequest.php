<?php

namespace App\Http\Admin\Request\User;

use Hyperf\Swagger\Annotation\Property;
use Hyperf\Swagger\Annotation\Schema;
use Hyperf\Validation\Request\FormRequest;


#[Schema(
    title: '用户设置',
    description: '用户设置',
    properties: [
        new Property(
            property: 'merge_switch',
            description: '合并开关 0:关闭 1:开启',
            type: 'integer',
        ),
        new Property(
            property: 'stock_config',
            description: '备货单配置',
            type: 'string',
        ),
        new Property(
            property: 'delivery_config',
            description: '发货单配置',
            type: 'string',
        ),
        new Property(
            property: 'pickup_stats_config',
            description: '拿货统计配置',
            type: 'string',
        ),
    ],
)]
class UserSettingRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'merge_switch' => 'integer|in:0,1',
            'stock_config' => 'sometimes|string',
            'delivery_config' => 'sometimes|string',
            'pickup_stats_config' => 'sometimes|string',
        ];
    }

    public function attributes(): array
    {
        return [
            'id' => '',
            'user_id' => '用户ID',
            'merge_switch' => '合并开关 0:关闭 1:开启',
            'stock_config' => '备货单配置',
            'delivery_config' => '发货单配置',
            'pickup_stats_config' => '拿货统计配置',
            'created_at' => '',
            'updated_at' => '',
        ];
    }

}