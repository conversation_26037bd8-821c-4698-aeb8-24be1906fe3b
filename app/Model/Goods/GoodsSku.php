<?php

namespace App\Model\Goods;

use App\Model\Product\ProductSku;
use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class goods_skus
* @property integer $id  
* @property integer $goods_id  商品id
* @property integer $user_id  用户id
* @property integer $shop_id  店铺id
* @property integer $type  订单类型
* @property string $sku_id  sku id
* @property string $sku_value  规格名称
* @property string $outer_id  商家外部编码（sku）
* @property string $outer_goods_id  商家外部编码（商品）
* @property string $custom_sku_value  自定义商品名称
* @property string $sku_pic  商品主图链接
* @property integer $is_onsale  上下架状态
* @property integer $created_by  创建者
* @property integer $updated_by  更新者
* @property string $created_at  
* @property string $updated_at  
* @property string $deleted_at  
*/
class GoodsSku extends MineModel
{
    protected ?string $table = 'goods_skus';

    protected array $fillable = ['id','goods_id','user_id','shop_id','type','sku_id','sku_value','outer_id','outer_goods_id','custom_sku_value','sku_pic','is_onsale','created_by','updated_by','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'integer','goods_id' => 'integer','user_id' => 'integer','shop_id' => 'integer','type' => 'integer','sku_id' => 'string','sku_value' => 'string','outer_id' => 'string','outer_goods_id' => 'string','custom_sku_value' => 'string','sku_pic' => 'string','is_onsale' => 'integer','created_by' => 'integer','updated_by' => 'integer','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    /**
     * 货品绑定的 sku
     * @return \Hyperf\Database\Model\Relations\BelongsToMany
     */
    public function productSkus()
    {
        return $this->belongsToMany(
            ProductSku::class,                    // 关联的模型
            'goods_product_relations',            // 中间表名
            'goods_sku_id',                      // 中间表中指向当前模型的外键
            'product_sku_id',                    // 中间表中指向关联模型的外键
            'id',                                // 当前模型的主键
            'id'                                 // 关联模型的主键
        )->wherePivot('bind_type', GoodsProductRelation::BIND_TYPE_GOODS)
            ->withPivot('pick_price');
    }

    /**
     * 货源绑定的 sku
     * @return \Hyperf\Database\Model\Relations\BelongsToMany
     */
    public function productSourceSkus()
    {
        return $this->belongsToMany(
            ProductSku::class,                    // 关联的模型
            'goods_product_relations',            // 中间表名
            'goods_sku_id',                      // 中间表中指向当前模型的外键
            'product_sku_id',                    // 中间表中指向关联模型的外键
            'id',                                // 当前模型的主键
            'id'                                 // 关联模型的主键
        )->wherePivot('bind_type', GoodsProductRelation::BIND_TYPE_PRODUCT_SOURCE)
            ->withPivot('pick_price');
    }

    /**
     * 关联商品
     * @return \Hyperf\Database\Model\Relations\BelongsTo
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }

}