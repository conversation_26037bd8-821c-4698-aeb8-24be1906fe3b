<?php

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class user_settings
* @property integer $id
* @property integer $user_id  用户ID
* @property integer $merge_switch  合并开关 0:关闭 1:开启
* @property string $stock_config  备货单配置
* @property string $delivery_config  发货单配置
* @property string $pickup_stats_config  拿货统计配置
* @property string $created_at
* @property string $updated_at
*/
class UserSetting extends MineModel
{
    const KEY_MERGE_SWITCH = 'merge_switch';
    const KEY_STOCK_CONFIG = 'stock_config';
    const KEY_DELIVERY_CONFIG = 'delivery_config';
    const KEY_PICKUP_STATS_CONFIG = 'pickup_stats_config';

    protected ?string $table = 'user_settings';

    protected array $fillable = ['id','user_id','merge_switch','stock_config','delivery_config','pickup_stats_config','created_at','updated_at',];

    protected array $casts = ['id' => 'integer','user_id' => 'integer','merge_switch' => 'integer','stock_config' => 'string','delivery_config' => 'string','pickup_stats_config' => 'string','created_at' => 'string','updated_at' => 'string',];
}