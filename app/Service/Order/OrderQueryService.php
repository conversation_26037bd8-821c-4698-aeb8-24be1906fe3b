<?php

namespace App\Service\Order;

use App\Constants\OrderConst;
use App\Constants\OrderTagConst;
use App\Http\Admin\Request\Order\OrderListRequest;
use App\Model\Order\OrderItemRelation;
use App\Model\Permission\User;
use App\Repository\Order\OrderItemRelationRepository;
use App\Repository\Order\OrderItemRepository;
use App\Repository\Order\OrderRepository;
use App\Service\IService;
use App\Utils\Log;
use App\Utils\OrderUtil;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;

class OrderQueryService extends IService
{
    public function __construct(
        protected readonly OrderRepository $repository,
        protected readonly OrderItemRepository $orderItemRepository,
        protected readonly OrderItemRelationRepository $orderItemRelationRepository,
    )
    {

    }

    public function list(User $user, OrderListRequest $request)
    {
//        $shopIds = $this->getAllShopIds($user);
        $bindShopIds = $this->getBindShopIds($user);
        $merchantShopIds = $this->getMerchantShopIds($user);
        $allShopIds = array_merge($bindShopIds, $merchantShopIds);
        $query = $this->orderItemRepository->getQuery();
        $queryParams = [];
        // 列表类型：1待发货，2已发货，3代发订单，4采购订单，5锁订单,6异常订单
        switch ($request->list_type) {
            case 1: // 待发货
            default:
                $query->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT);
//                $query->whereIn('assignee_id', [0]);
                $query->where('order_items.pick_task_id', 0);
                $this->orderItemRepository->builderByShopIds($query, $user, $bindShopIds, $merchantShopIds);
            break;
            case 2: // 已发货
                $query->whereIn('order_items.shop_id', $bindShopIds);
                $query->whereIn('orders.order_status', [OrderConst::ORDER_STATUS_DELIVERED]);
                $query->where('order_items.assign_status', 0);
                $query->where('order_items.pick_task_id', 0);
                break;
            case 3: // 代发订单
                $query->whereIn('order_items.shop_id', $bindShopIds);
                $query->where('order_items.assign_status', 1);
                $query->where('order_items.pick_task_id', 0);
                break;
            case 4: // 采购订单
                $query->whereIn('order_items.shop_id', $bindShopIds);
                $query->where('order_items.assign_status', 0);
                $query->where('order_items.pick_task_id', '>', 0);
                break;
            case 5: // 锁订单
                $query->whereIn('order_items.shop_id', $bindShopIds);
                $query->whereNotNull('order_items.orders.locked_at');
                break;
            case 6: // 异常订单
                $query->whereIn('order_items.shop_id', $bindShopIds);
                if ($request->abnormal_type == OrderConst::ABNORMAL_TYPE_ORDER_CLOSE){
                    $query->whereIn('orders.order_status', [OrderConst::ORDER_STATUS_CLOSED]);
                }
                break;
        }

        $query->where($queryParams);
        $query->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');
        $query->leftJoin('order_item_relations', 'order_item_relations.order_item_id', '=', 'order_items.id');
        $query->where('order_item_relations.user_id', $user->id);
        $with = ['productSkus.product:id,name,product_no', 'order', 'orderCipherInfo'];
        $this->handleQueryByRequest($query, $user, $request);
        return $this->queryList($query,$user, $request, $with);
    }

    /**
     * @param Builder $query
     * @param array $shopIds
     * @param OrderListRequest $request
     * @param array $with
     * @return array
     */
    public function queryList(Builder $query,$user, OrderListRequest $request, array $with): array
    {
        // 直接查询所有数据，不分页
        $groupBy = 'system_order_no';
        $query->groupBy([$groupBy]);
        $query->select(
            Db::raw('group_concat(order_items.id) as ids'),
            Db::raw('group_concat(orders.address_md5) as address_md5s'),
            Db::raw("$groupBy as groupField"),
        );

        $query->orderBy('order_items.id', 'desc');
        $list = $query->forPage($request->page, $request->page_size)->get();

        $idArr = $list->pluck('ids')->map(function ($item) {
            return explode(',', $item);
        })->flatten()->unique()->values()->toArray();
        $addressMd5Arr = $list->pluck('address_md5s')->map(function ($item) {
            return explode(',', $item);
        })->flatten()->unique()->values()->toArray();


        $finallyQuery = $this->orderItemRepository->getQuery();
        $finallyQuery->whereIn('order_items.id', $idArr);
        $finallyQuery->leftJoin('order_item_relations', 'order_item_relations.order_item_id', '=', 'order_items.id');
        $finallyQuery->where('order_item_relations.user_id', $user->id);
        $finallyQuery->with($with);
        $finallyList = $finallyQuery->get(['order_items.*','order_item_relations.system_order_no','order_item_relations.split_merge_flag', Db::raw("$groupBy as groupField")]);

        $allMergeableData = [];
        if ($request->is_merge){
            // 预查询所有可合并的订单数据 - 只统计待发货状态的订单，按 address_md5 分组并计算 count
            $allMergeableData = $this->orderItemRepository->getQuery()
                ->whereIn('address_md5', $addressMd5Arr)
                ->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT) // 只查询待发货状态
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->select(Db::raw('count(*) as count, address_md5'))
                ->groupBy('address_md5')
                ->get()
                ->keyBy('address_md5')->toArray();
        }
        // 按 system_order_no 分组并构造返回结构
        $groupedItems = [];
        $finallyList->groupBy('groupField')->map(function ($items, $key) use ($allMergeableData,$request,&$groupedItems) {
            $first = $items->first();
            $count = $items->count();
            $tidCount = $items->unique('tid')->count();
            $tags = [];
            if ($tidCount > 1){
                $tags[OrderTagConst::IS_MERGE] = 1;
            }
            if ($first->split_merge_flag == OrderConst::SPLIT_MERGE_FLAG_SPLIT){
                $tags[OrderTagConst::IS_SPLIT] = 1;
            }
            // 是否代发
            if ($first->assign_status == 1){
                $tags[OrderTagConst::IS_SUPPLIER_ORDER] = 1;
            }

            // 检查是否可合并 - 使用预查询的数据
            $addressMd5 = $first->order->address_md5 ?? '';

            // 判断是否可合
            if ($addressMd5 && isset($allMergeableData[$addressMd5])) {
                $mergeableInfo = $allMergeableData[$addressMd5];

                if ($mergeableInfo['count'] > $count) {
                    $tags[OrderTagConst::IS_MERGEABLE] = 1;
                }
            }

            $groupedItems[] = [
                'system_order_no' => (string)$key,
                'groupField' => (string)$key,
                'tags' => $tags,
                'address_md5' => $first->order->address_md5 ?? '',
                'itemCount' => $count,
                'items' => $items->toArray(),
            ];
        });
        Log::info('groupedItems',[$groupedItems]);

        // 使用 array_multisort 根据 $list 的排序重新排序
        $orderMap = array_flip(array_column($list->toArray(), 'groupField'));
        // 2. 为 $sortOrder 生成一个临时的顺序数组
        $sortOrder = [];
        foreach ($groupedItems as $item) {
            // 同样，未找到的元素给予一个很大的值
            $sortOrder[] = $orderMap[$item['groupField']] ?? PHP_INT_MAX;
        }
        // 3. 使用 array_multisort 进行排序
        array_multisort($sortOrder, SORT_ASC, $groupedItems);

        return [
            'total' => count($groupedItems),
            'list' => $groupedItems,
        ];
    }
    
    /**
     * 检查订单项是否可以合并
     * @param User $user
     * @param array $orderItemIds
     * @return array
     */
    public function checkMergeable(User $user, array $orderItemIds): array
    {
        $shopIds = $this->getAllShopIds($user);
        
        // 查询订单项并关联订单信息
        $orderItems = $this->orderItemRepository->getQuery()
            ->whereIn('order_items.id', $orderItemIds)
            ->whereIn('order_items.shop_id', $shopIds)
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select('order_items.id', 'orders.address_md5')
            ->get();
        
        // 按 address_md5 分组
        $groupedItems = $orderItems->groupBy('address_md5')->map(function ($items) {
            return [
                'address_md5' => $items->first()->address_md5,
                'itemCount' => $items->count(),
                'items' => $items->pluck('id')->toArray()
            ];
        })->filter(function ($group) {
            // 过滤掉数量小于1的组
            return $group['itemCount'] >= 1;
        })->values();
        
        return $groupedItems->toArray();
    }
    
    /**
     * 查询可合并的订单列表
     * @param User $user 用户
     * @param int $orderItemId 订单项ID
     * @return array
     */
    public function getMergeableOrderItemList(User $user, int $orderItemId): array
    {
        $shopIds = $this->getAllShopIds($user);
        
        // 先获取指定订单项的 receiver_md5
        $orderItem = $this->orderItemRepository->getQuery()
            ->where('order_items.id', $orderItemId)
            ->whereIn('order_items.shop_id', $shopIds)
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select('order_items.id', 'orders.address_md5', 'order_items.shop_id')
            ->first();
            
        if (!$orderItem) {
            return [];
        }
        
        // 查询具有相同 receiver_md5 的其他订单项
        $mergeableOrderItems = $this->orderItemRepository->getQuery()
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            //            ->where('order_items.id', '!=', $orderItemId) // 排除自己
            ->where('order_items.shop_id', $orderItem->shop_id)
            ->where('orders.address_md5', $orderItem->address_md5)
            ->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT)
            ->with(['orderItemRelation:id,order_item_id,system_order_no','orderCipherInfo'])
            ->select(['order_items.*','orders.order_type'])
            ->get()
            ->toArray();
            
        return $mergeableOrderItems;
    }

    public function takeList(User $user, OrderListRequest $request): array
    {
        $bindShopIds = $this->getBindShopIds($user);
        $merchantShopIds = $this->getMerchantShopIds($user);
        $queryParams = [];
//        $queryParams['shop_ids'] = $shopIds;
        $queryParams['order_by'] = 'order_items.id';
        $with = ['productSourceSkus.product:id,name,product_no', 'order', 'orderCipherInfo','goodsSku:id,goods_id,sku_id,sku_value'];
        $query = $this->orderItemRepository->getQuery()
            ->leftJoin('order_item_relations', 'order_item_relations.order_item_id', '=', 'order_items.id')
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');
        $query->where('order_items.pick_task_id', 0);
        $query->where('order_items.order_status', OrderConst::ORDER_STATUS_PAYMENT);
        $query->where('order_item_relations.user_id', $user->id);
        $this->orderItemRepository->builderByShopIds($query, $user, $bindShopIds, $merchantShopIds);
        $query->select(['order_items.id']);
        $list = $this->orderItemRepository->perQuery($query, $queryParams)->get();

        $orderIdArr = $list->pluck('id')->toArray();
        $lastQueryParams = [
            'ids' => $orderIdArr,
        ];
        $pageWith = $this->orderItemRepository->pageWith($lastQueryParams, $with, $request->page, $request->page_size);
        
        // 循环过滤productSourceSkus，只保留assign_status=0的数据
        foreach ($pageWith['list'] as $index => $orderItem) {
            if ($orderItem['assign_status'] != 0) {
                $pageWith['list'][$index]['product_source_skus'] = [];
            }
        }
        
        return $pageWith;
    }

    /**
     * 根据店铺ID和订单号查询订单数据
     * @param int $shopId
     * @param string $tid
     * @return mixed
     */
    public function getByShopIdAndTid(int $shopId, string $tid)
    {
        $with = ['items','orderCipherInfo'];

        return $this->repository->getQuery()
            ->where('shop_id', $shopId)
            ->where('tid', $tid)
            ->with($with)
            ->first();
    }


    private function handleQueryByRequest(Builder $query, User $user, OrderListRequest $request)
    {
        if ($request->abnormal_type > 0) {
            $query->where('orders.abnormal_type', $request->abnormal_type);
        }else{
            $query->where('orders.abnormal_type', 0);
        }
    }

}