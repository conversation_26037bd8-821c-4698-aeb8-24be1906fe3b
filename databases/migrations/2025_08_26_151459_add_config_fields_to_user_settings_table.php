<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_settings', function (Blueprint $table) {
            $table->text('stock_config')->nullable()->comment('备货单配置');
            $table->text('delivery_config')->nullable()->comment('发货单配置');
            $table->text('pickup_stats_config')->nullable()->comment('拿货统计配置');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_settings', function (Blueprint $table) {
            $table->dropColumn(['stock_config', 'delivery_config', 'pickup_stats_config']);
        });
    }
};
