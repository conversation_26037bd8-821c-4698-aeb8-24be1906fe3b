<script setup>
const props = defineProps({
  goodsSet: {
    type: Object,
    default: {},
  }
});
const picSizeList = [
  {label: '小', value: 1, size: 50},
  {label: '中', value: 2, size: 80},
  {label: '大', value: 3, size: 100},
]
const mergeSetList = [
  {label: '不进行合并', value: 1},
  {label: '相同货品名称合并', value: 2},
  {label: '相同货品编码合并', value: 3},
]

const emit = defineEmits(['changeConfig']);
</script>

<template>
  <el-popover width="320" placement="bottom" transfer trigger="click">
    <template #reference>
      <ma-svg-icon
        className="pointer mt-1"
        name="material-symbols:settings-rounded"
        :size="18"
      />
    </template>
    <div style="padding: 8px 16px;">
      <h3>货品信息设置</h3>
      <div class="goods_info_set">
        <el-checkbox :value="goodsSet.picShow"
                  @change="(e)=>emit('changeConfig',{type:'goodsSet',fieldName:'picShow',value:e})">
          <div class="flex_center">
            货品图片
            <el-radio-group v-model="goodsSet.picSize"  class="ml-2" size="small"
                            @change="(e)=>emit('changeConfig',{type:'goodsSet',fieldName:'picSize',value:e})">
              <el-radio-button v-for="it in picSizeList" :key="it.value" :label="it.value">{{ it.label }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </el-checkbox>
        <el-checkbox :value="goodsSet.productName"
                     @change="(e)=>emit('changeConfig',{type:'goodsSet',fieldName:'productName',value:e})">
          货品名称
        </el-checkbox>
        <el-checkbox :value="goodsSet.productCode"
                     @change="(e)=>emit('changeConfig',{type:'goodsSet',fieldName:'productCode',value:e})">
          货品编码
        </el-checkbox>
      </div>
      <h3 class="mt-2">货品合并设置</h3>
      <div class="goods_info_set">
        <el-radio-group v-model="goodsSet.mergeSet"  style="display: grid"
                    @change="(e)=>emit('changeConfig',{type:'goodsSet',fieldName:'mergeSet',value:e,reload: true})">
          <el-radio v-for="it in mergeSetList" :key="it.value" :label="it.value">{{ it.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </el-popover>
</template>

<style lang="scss">
.goods_info_set {
  display: grid;
  margin-top: 10px;
  border-radius: 4px;
  background-color: #f7f8fa;
  padding: 5px 10px;
}
</style>
