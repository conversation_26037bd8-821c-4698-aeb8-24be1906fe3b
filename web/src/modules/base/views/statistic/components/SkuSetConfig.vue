<script setup>
const props = defineProps({
  skuSet: {
    type: Object,
    default: {},
  }
});
const picSizeList = [
  {label: '小', value: 1, size: 50},
  {label: '中', value: 2, size: 80},
  {label: '大', value: 3, size: 100},
]
const titleTypeList = [
  {label: '优先显示简称没有简称显示名称', value: 1},
  {label: '仅显示简称', value: 2},
  {label: '仅显示名称', value: 3},
]
const mergeSetList = [
  {label: '不进行合并', value: 1},
  {label: '相同规格简称合并', value: 2},
  {label: '相同规格标题合并', value: 3},
  {label: '相同规格编码合并', value: 4},
  {label: '相同货品规格名称合并', value: 5},
  {label: '相同货品规格编码合并', value: 6},
]

const emit = defineEmits(['changeConfig']);
</script>

<template>
  <el-popover width="320" placement="bottom" transfer trigger="click">
    <template #reference>
      <ma-svg-icon
        className="pointer mt-1"
        name="material-symbols:settings-rounded"
        :size="18"
      />
    </template>
    <div style="padding: 8px 16px;">
      <h3>规格信息设置</h3>
      <div class="sku_info_set">
        <el-checkbox :value="skuSet.picShow"
                     @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'picShow',value:e})">
          <div class="flex_center">
            规格图片
            <el-radio-group v-model="skuSet.picSize"  class="ml-2" size="small"
                            @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'picSize',value:e})">
              <el-radio-button v-for="it in picSizeList" :key="it.value" :label="it.value">{{ it.label }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </el-checkbox>
        <div class="flex_top">
          <el-checkbox :value="true" disabled/>
          <div>
            <div>规格名称或简称</div>
            <el-radio-group v-model="skuSet.showTitleType"  style="display: grid"
                            @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'showTitleType',value:e})">
              <el-radio v-for="it in titleTypeList" :key="it.value" :label="it.value">{{ it.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        <el-checkbox :value="skuSet.showId"
                     @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'showId',value:e})">
          规格ID
        </el-checkbox>
        <el-checkbox :value="skuSet.showOuterId"
                     @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'showOuterId',value:e})">
          规格编码
        </el-checkbox>
        <el-checkbox :value="skuSet.productSkuName"
                     @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'productSkuName',value:e})">
          货品规格名称
        </el-checkbox>
        <el-checkbox :value="skuSet.productSkuCode"
                     @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'productSkuCode',value:e})">
          货品规格编码
        </el-checkbox>
      </div>
      <h3 class="mt-2">规格合并设置</h3>
      <div class="sku_info_set">
        <el-radio-group v-model="skuSet.mergeSet" style="display: grid"
                        @change="(e)=>emit('changeConfig',{type:'skuSet',fieldName:'mergeSet',value:e,reload: true})">
          <el-radio v-for="it in mergeSetList" :key="it.value" :label="it.value">{{ it.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
  </el-popover>
</template>

<style lang="scss">
.sku_info_set {
  display: grid;
  margin-top: 10px;
  border-radius: 4px;
  background-color: #f7f8fa;
  padding: 5px 10px;
}
</style>
