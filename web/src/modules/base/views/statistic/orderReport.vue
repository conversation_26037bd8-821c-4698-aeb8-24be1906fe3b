<script setup>
import OrderPickingList from "~/base/views/statistic/orderPickingList.vue";
import CheckAccount from "~/base/views/statistic/components/checkAccount.vue";

const activeMode = ref(1)
</script>

<template>
  <div>
    <div class="order-report">
      <el-tabs v-model="activeMode" class="tab-select">
        <el-tab-pane label="备货单" :name="1"/>
        <el-tab-pane label="发货统计" :name="2"/>
      </el-tabs>
    </div>
    <order-picking-list v-if="activeMode === 1" />
    <check-account v-if="activeMode === 2"/>
  </div>
</template>


<style lang="scss">
.order-report {
  margin: 0.75rem;
  .tab-select {
    background: white;
    padding: 0.75rem;
  }
}
</style>
